import { FastifyReply, FastifyRequest } from 'fastify';
import { z } from 'zod';
import { ApprovalService } from '../services/approval.js';
import { ProjectService } from '../services/project.js';
import { ContractEntity, PaymentMethod } from '../types/approval.js';
import {
  CreateFundingPlanRequest,
  FundingPlanQueryParams,
  FundingPlanStatus,
  UpdateFundingPlanRequest
} from '../types/project.js';
import { BudgetService } from '../services/budget.js';

// 验证模式
const createFundingPlanSchema = z.object({
  title: z.string().min(1, '计划标题不能为空').max(200, '计划标题不能超过200字符'),
  year: z.number().int().min(2020).max(2050, '年份必须在2020-2050之间'),
  month: z.number().int().min(1).max(12, '月份必须在1-12之间'),
  weekOfMonth: z.number().int().min(1).max(5, '周数必须在1-5之间'),
  plannedAmount: z.number().min(0.01, '计划金额必须大于0'),
  remarks: z.string().optional(),
  budgetId: z.string().min(1, '预算ID不能为空'),
});

const updateFundingPlanSchema = z.object({
  id: z.string().min(1, '资金计划ID不能为空'),
  title: z.string().min(1).max(200).optional(),
  year: z.number().int().min(2020).max(2050).optional(),
  month: z.number().int().min(1).max(12).optional(),
  weekOfMonth: z.number().int().min(1).max(5).optional(),
  plannedAmount: z.number().min(0, '计划金额不能为负数').optional(),
  paidAmount: z.number().min(0, '已付金额不能为负数').optional(),
  status: z.nativeEnum(FundingPlanStatus).optional(),
  approvalAmount: z.number().min(0, '审批金额不能为负数').optional(),
  approvalReason: z.string().optional(),
  remarks: z.string().optional(),
}).refine((data) => {
  // 如果同时提供了计划金额和已付金额，验证已付金额不能超过计划金额
  if (data.plannedAmount !== undefined && data.paidAmount !== undefined) {
    return data.paidAmount <= data.plannedAmount;
  }
  return true;
}, {
  message: '已付金额不能超过计划金额',
  path: ['paidAmount']
});

const fundingPlanQuerySchema = z.object({
  page: z.coerce.number().min(1).optional().default(1),
  pageSize: z.coerce.number().min(1).max(100).optional().default(20),
  budgetId: z.string().optional(),
  projectId: z.string().optional(),
  year: z.coerce.number().int().optional(),
  month: z.coerce.number().int().min(1).max(12).optional(),
  weekOfMonth: z.coerce.number().int().min(1).max(5).optional(),
  status: z.nativeEnum(FundingPlanStatus).optional(),
  sortBy: z.enum(['year', 'month', 'weekOfMonth', 'plannedAmount', 'createdAt']).optional().default('year'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

// 批量创建资金计划验证模式
const batchCreateFundingPlanSchema = z.object({
  plans: z.array(createFundingPlanSchema).min(1, '至少需要一个资金计划').max(50, '单次最多创建50个资金计划'),
}).refine((data) => {
  // 验证同一预算下的年月周组合不能重复
  const combinations = new Set();
  for (const plan of data.plans) {
    const key = `${plan.budgetId}-${plan.year}-${plan.month}-${plan.weekOfMonth}`;
    if (combinations.has(key)) {
      return false;
    }
    combinations.add(key);
  }
  return true;
}, {
  message: '批量创建中存在重复的预算年月周组合',
  path: ['plans']
});

// 审批请求验证模式
const createApprovalSchema = z.object({
  totalAmount: z.number().min(0.01, '付款总额必须大于0'),
  paymentReason: z.string().min(1, '付款事由不能为空'),
  contractEntity: z.nativeEnum(ContractEntity, { errorMap: () => ({ message: '无效的合同签署主体' }) }),
  expectedPaymentDate: z.string().refine((date) => !isNaN(Date.parse(date)), '无效的期望付款时间格式'),
  department: z.number().int().min(1, '申请部门不能为空'),
  paymentMethod: z.nativeEnum(PaymentMethod, { errorMap: () => ({ message: '无效的付款方式' }) }),
  receivingAccount: z.object({
    accountName: z.string().min(1, '账户名称不能为空'),
    accountNumber: z.string().min(1, '账号不能为空'),
    bankName: z.string().min(1, '开户银行不能为空'),
    bankCode: z.string().optional(),
  }),
  relatedApprovalId: z.string().optional(),
  invoiceFiles: z.array(z.any()).optional(),
  attachments: z.array(z.any()).optional(),
  remark: z.string().optional(),
});

export class FundingPlanController {
  private projectService: ProjectService;
  private approvalService: ApprovalService;
  private budgetService: BudgetService

  constructor() {
    this.projectService = new ProjectService();
    this.approvalService = new ApprovalService();
    this.budgetService = new BudgetService();
  }

  // 创建资金计划
  async createFundingPlan(request: FastifyRequest, reply: FastifyReply) {
    try {
      const body = request.body;
      const userId = (request as any).user?.userid;

      if (!userId) {
        return reply.status(401).send({ error: '用户未认证' });
      }

      // 验证请求数据
      const validatedData = createFundingPlanSchema.parse(body);

      // 获取预算可用金额
      const availableAmount = await this.budgetService.getBudgetAvailableAmount(validatedData.budgetId);
      console.log('[ availableAmount ] >', availableAmount)
      if (validatedData.plannedAmount > availableAmount) {
        return reply.status(400).send({
          error: '预算余额不足',
          message: `预算余额不足，当前可用金额：${availableAmount}`
        });
      }


      // 创建资金计划
      const fundingPlan = await this.projectService.createFundingPlan(
        validatedData as CreateFundingPlanRequest,
        userId
      );

      reply.send({
        success: true,
        data: fundingPlan,
        message: '资金计划创建成功'
      });
    } catch (error: any) {
      console.error('创建资金计划失败:', error);

      if (error.name === 'ZodError') {
        return reply.status(400).send({
          error: '请求参数验证失败',
          details: error.errors
        });
      }

      // 处理特定的业务错误
      if (error.message?.includes('预算不存在')) {
        return reply.status(400).send({
          error: '预算不存在',
          message: '指定的预算ID不存在，请检查预算是否已创建'
        });
      }

      if (error.message?.includes('已存在资金计划')) {
        return reply.status(400).send({
          error: '资金计划已存在',
          message: error.message
        });
      }

      // 处理外键约束错误
      if (error.code === 'P2003' || error.message?.includes('Foreign key constraint')) {
        return reply.status(400).send({
          error: '关联数据不存在',
          message: '指定的预算ID不存在，请检查预算是否已创建'
        });
      }

      reply.status(500).send({
        error: '创建资金计划失败',
        message: error.message
      });
    }
  }

  // 批量创建资金计划 - 严格事务模式
  async createBatchFundingPlans(request: FastifyRequest, reply: FastifyReply) {
    try {
      const body = request.body;
      const userId = (request as any).user?.userid;

      if (!userId) {
        return reply.status(401).send({ error: '用户未认证' });
      }

      // 验证请求数据
      const validatedData = batchCreateFundingPlanSchema.parse(body);

      // 批量创建资金计划 - 严格事务模式
      const createdPlans = await this.projectService.createBatchFundingPlans(
        validatedData.plans as CreateFundingPlanRequest[],
        userId
      );

      reply.send({
        success: true,
        data: {
          createdPlans,
          totalCount: createdPlans.length
        },
        message: `成功批量创建 ${createdPlans.length} 个资金计划`
      });
    } catch (error: any) {
      console.error('批量创建资金计划失败:', error);

      if (error.name === 'ZodError') {
        return reply.status(400).send({
          success: false,
          error: '请求参数验证失败',
          message: '请求数据格式不正确',
          details: error.errors
        });
      }

      // 处理特定的业务错误
      if (error.message?.includes('预算不存在')) {
        return reply.status(400).send({
          success: false,
          error: '预算不存在',
          message: error.message
        });
      }

      if (error.message?.includes('预算余额不足')) {
        return reply.status(400).send({
          success: false,
          error: '预算余额不足',
          message: error.message
        });
      }

      if (error.message?.includes('资金计划已存在') || error.message?.includes('重复的年月周组合')) {
        return reply.status(400).send({
          success: false,
          error: '资金计划冲突',
          message: error.message
        });
      }

      // 处理数据库约束错误
      if (error.code === 'P2002') {
        return reply.status(400).send({
          success: false,
          error: '数据冲突',
          message: '存在重复的资金计划，请检查年月周组合是否唯一'
        });
      }

      // 处理外键约束错误
      if (error.code === 'P2003') {
        return reply.status(400).send({
          success: false,
          error: '关联数据不存在',
          message: '指定的预算ID不存在，请检查预算是否已创建'
        });
      }

      reply.status(500).send({
        success: false,
        error: '批量创建资金计划失败',
        message: error.message || '服务器内部错误'
      });
    }
  }

  // 获取资金计划列表
  async getFundingPlans(request: FastifyRequest, reply: FastifyReply) {
    try {
      const query = request.query;

      // 验证查询参数
      const validatedQuery = fundingPlanQuerySchema.parse(query);

      // 获取资金计划列表
      const result = await this.projectService.getFundingPlans(validatedQuery as FundingPlanQueryParams);

      reply.send({
        success: true,
        data: result,
        message: '获取资金计划列表成功'
      });
    } catch (error: any) {
      console.error('获取资金计划列表失败:', error);

      if (error.name === 'ZodError') {
        return reply.status(400).send({
          error: '查询参数验证失败',
          details: error.errors
        });
      }

      reply.status(500).send({
        error: '获取资金计划列表失败',
        message: error.message
      });
    }
  }

  // 获取单个资金计划
  async getFundingPlan(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };

      const fundingPlan = await this.projectService.getFundingPlan(id);

      if (!fundingPlan) {
        return reply.status(404).send({
          error: '资金计划不存在'
        });
      }

      reply.send({
        success: true,
        data: fundingPlan,
        message: '获取资金计划详情成功'
      });
    } catch (error: any) {
      console.error('获取资金计划详情失败:', error);
      reply.status(500).send({
        error: '获取资金计划详情失败',
        message: error.message
      });
    }
  }

  // 更新资金计划
  async updateFundingPlan(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };
      const body = request.body;
      const userId = (request as any).user?.userid;

      if (!userId) {
        return reply.status(401).send({ error: '用户未认证' });
      }

      // 验证请求数据
      const validatedData = updateFundingPlanSchema.parse({ id, ...body });

      // 更新资金计划
      const fundingPlan = await this.projectService.updateFundingPlan(
        validatedData as UpdateFundingPlanRequest,
        userId
      );

      reply.send({
        success: true,
        data: fundingPlan,
        message: '资金计划更新成功'
      });
    } catch (error: any) {
      console.error('更新资金计划失败:', error);

      if (error.name === 'ZodError') {
        return reply.status(400).send({
          error: '请求参数验证失败',
          details: error.errors
        });
      }

      reply.status(500).send({
        error: '更新资金计划失败',
        message: error.message
      });
    }
  }

  // 删除资金计划
  async deleteFundingPlan(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };

      await this.projectService.deleteFundingPlan(id);

      reply.send({
        success: true,
        message: '资金计划删除成功'
      });
    } catch (error: any) {
      console.error('删除资金计划失败:', error);
      reply.status(500).send({
        error: '删除资金计划失败',
        message: error.message
      });
    }
  }

  // 提交资金计划审批
  async submitApproval(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };
      const body = request.body;
      const userId = (request as any).user?.userid;

      if (!userId) {
        return reply.status(401).send({ error: '用户未认证' });
      }

      // 验证请求数据
      const validatedData = createApprovalSchema.parse(body);

      // 提交审批
      const approval = await this.approvalService.createApproval({
        fundingPlanId: id,
        ...validatedData
      }, userId);

      reply.send({
        success: true,
        data: approval,
        message: '审批提交成功'
      });
    } catch (error: any) {
      console.error('提交审批失败:', error);

      if (error.name === 'ZodError') {
        return reply.status(400).send({
          error: '请求参数验证失败',
          details: error.errors
        });
      }

      reply.status(500).send({
        error: '提交审批失败',
        message: error.message
      });
    }
  }

  // 获取资金计划统计
  async getFundingPlanStats(request: FastifyRequest, reply: FastifyReply) {
    try {
      const stats = await this.projectService.getFundingPlanStats();

      reply.send({
        success: true,
        data: stats,
        message: '获取资金计划统计成功'
      });
    } catch (error: any) {
      console.error('获取资金计划统计失败:', error);
      reply.status(500).send({
        error: '获取资金计划统计失败',
        message: error.message
      });
    }
  }
}
