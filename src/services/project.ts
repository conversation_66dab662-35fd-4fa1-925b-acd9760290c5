import {
  Attachment,
  Brand,
  BrandFinancialDetail,
  BrandFinancialSummary,
  BrandListResponse,
  BrandQueryParams,
  Budget,
  BudgetListResponse,
  BudgetQueryParams,
  BudgetStats,
  ContractSigningStatus,
  ContractType,
  CreateBrandRequest,
  CreateBudgetRequest,
  CreateFundingPlanRequest,
  CreateProjectRequest,
  CreateProjectRevenueRequest,
  CreateSupplierRequest,
  CreateWeeklyBudgetRequest,
  DocumentType,
  FileUploadResponse,
  FinancialReportQueryParams,
  FinancialReportResponse,
  FundingPlan,
  FundingPlanListResponse,
  FundingPlanQueryParams,
  FundingPlanStats,
  Project,
  ProjectListResponse,
  ProjectProfit,
  ProjectQueryParams,
  ProjectRevenue,
  ProjectRevenueListResponse,
  ProjectRevenueQueryParams,
  ProjectStats,
  ProjectStatus,
  ProjectTemplate,
  RevenueStats,
  RevenueStatus,
  ServiceType,
  Supplier,
  SupplierListResponse,
  SupplierQueryParams,
  SupplierStats,
  TaxRate,
  UpdateBrandRequest,
  UpdateBudgetRequest,
  UpdateFundingPlanRequest,
  UpdateProjectRequest,
  UpdateProjectRevenueRequest,
  UpdateSupplierRequest,
  UpdateWeeklyBudgetRequest,
  User,
  WeeklyBudget,
  WeeklyBudgetListResponse,
  WeeklyBudgetQueryParams,
  WeeklyBudgetStats,
  WeeklyBudgetStatus
} from '../types/project.js';
import { TimezoneUtils } from '../utils/timezone.js';
import { db } from './database.js';
import { DingTalkService } from './dingtalk.js';
import { DingTalkNotificationService } from './dingtalkNotification.js';
import { UserSyncService } from './userSync.js';

// CSV数据类型定义
interface WeeklyBudgetCSVData {
  projectName: string;
  supplierName: string;
  serviceType: string;
  serviceContent: string;
  contractAmount: number;
  taxRate: TaxRate;
  taxExclusiveAmount: number;
  paidAmount: number;
  unpaidAmount: number;
}

export class ProjectService {
  private dingTalkService: DingTalkService;
  private notificationService: DingTalkNotificationService;
  private userSyncService: UserSyncService;
  private useDatabase: boolean;

  // 模拟数据存储（当不使用数据库时）
  private projects: Map<string, Project> = new Map();
  private brands: Map<string, Brand> = new Map();
  private attachments: Map<string, Attachment> = new Map();
  private templates: Map<string, ProjectTemplate> = new Map();

  constructor() {
    this.dingTalkService = new DingTalkService();
    this.notificationService = new DingTalkNotificationService();
    this.userSyncService = new UserSyncService(db, this.dingTalkService);
    // 强制使用数据库模式
    this.useDatabase = true;
  }
  // 计算项目利润
  // 注意：estimatedInfluencerRebate（达人返点）虽然在cost对象中，但实际上是收入性质
  // 利润计算公式：项目规划预算 - (达人成本 + 投流成本 + 其他成本 + 居间费) + 达人返点
  private calculateProfit(budget: Project['budget'], cost: Project['cost']): ProjectProfit {
    const totalCost = cost.influencerCost + cost.adCost + cost.otherCost + cost.intermediaryCost;
    const profit = budget.planningBudget - totalCost + cost.estimatedInfluencerRebate;
    const grossMargin = budget.planningBudget > 0 ? (profit / budget.planningBudget) * 100 : 0;

    return {
      profit: Math.round(profit * 100) / 100,
      grossMargin: Math.round(grossMargin * 100) / 100
    };
  }

  // 生成唯一ID
  private generateId(prefix: string): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // 创建变更记录的辅助方法
  private async createChangeLog(data: {
    changeType: string;
    changeTitle: string;
    changeDetails?: any;
    beforeData?: any;
    afterData?: any;
    changedFields: string[];
    operatorId: string;
    operatorName: string;
    operatorIP?: string;
    userAgent?: string;
    reason?: string;
    description?: string;
    projectId: string;
  }) {
    if (this.useDatabase) {
      try {
        await db.createProjectChangeLog(data);
      } catch (error) {
        console.error('创建变更记录失败:', error);
      }
    }
    // 如果不使用数据库，暂时不记录变更日志
  }

  // 比较对象差异，获取变更字段
  // 核心业务逻辑：智能字段对比，只记录实际发生变化的字段
  // 解决问题：避免记录"伪变更"（如字段值从1更新为1的情况）
  private getChangedFields(before: any, after: any, updateRequest?: any): string[] {
    const changedFields: string[] = [];

    // 排除不应该被记录为变更的字段（系统自动维护的字段和关联信息字段）
    const excludeFields = new Set([
      'id', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy',
      // 排除关联信息字段（这些是数据库查询时自动关联的，不是用户更新的）
      'brand', 'executorPMInfo', 'contentMediaInfo', 'attachments'
    ]);

    // 如果有更新请求，只对比更新请求中包含的字段
    // 这样可以避免对比数据库关联查询出来的额外字段
    const keysToCompare = updateRequest
      ? new Set(Object.keys(updateRequest).filter(key => !excludeFields.has(key)))
      : new Set([...Object.keys(before || {}), ...Object.keys(after || {})].filter(key => !excludeFields.has(key)));

    for (const key of keysToCompare) {
      // 跳过排除的字段
      if (excludeFields.has(key)) {
        continue;
      }

      // 比较字段值是否发生变更
      const beforeValue = before?.[key];
      const afterValue = after?.[key];

      // 使用深度比较来检测变更
      // 重要：只有当JSON序列化后的值不同时，才认为字段发生了变更
      // 这确保了数值1->1、字符串"test"->"test"等情况不会被误判为变更
      if (JSON.stringify(beforeValue) !== JSON.stringify(afterValue)) {
        changedFields.push(key);
      }
    }

    return changedFields;
  }

  // 获取用户信息用于变更记录
  private async getUserInfoForChangeLog(userid: string): Promise<{ userid: string; name: string }> {
    try {
      const userInfo = await this.getUserInfo(userid);
      return {
        userid: userInfo.userid,
        name: userInfo.name
      };
    } catch (error) {
      return {
        userid,
        name: `用户${userid}`
      };
    }
  }

  // 转换数据库Brand对象为接口Brand
  private transformDbBrand(dbBrand: any): Brand {
    return {
      id: dbBrand.id,
      name: dbBrand.name,
      description: dbBrand.description || undefined,
      logo: dbBrand.logo || undefined,
      status: dbBrand.status.toLowerCase(),
      createdAt: dbBrand.createdAt,
      updatedAt: dbBrand.updatedAt,
      createdBy: dbBrand.createdBy
    };
  }

  // 项目管理方法

  // 创建项目
  async createProject(request: CreateProjectRequest, createdBy: string, operatorInfo?: { ip?: string; userAgent?: string }): Promise<Project> {
    if (this.useDatabase) {
      const project = await db.createProject(request, createdBy);

      // 记录创建变更日志
      const userInfo = await this.getUserInfoForChangeLog(createdBy);
      // 对于创建操作，记录所有非空/非默认值的字段
      const createdFields = Object.keys(request).filter(key => {
        const value = (request as any)[key];
        return value !== undefined && value !== null && value !== '';
      });

      await this.createChangeLog({
        changeType: 'CREATE',
        changeTitle: `创建项目: ${request.projectName}`,
        changeDetails: {
          action: 'create_project',
          projectData: request
        },
        afterData: project,
        changedFields: createdFields,
        operatorId: createdBy,
        operatorName: userInfo.name,
        operatorIP: operatorInfo?.ip,
        userAgent: operatorInfo?.userAgent,
        description: `创建了新项目"${request.projectName}"，设置了${createdFields.length}个字段`,
        projectId: project.id
      });

      // 发送项目创建通知给执行PM
      await this.sendProjectCreatedNotification(project, createdBy);

      return project;
    }

    const id = this.generateId('project');

    // 验证品牌是否存在
    const brand = this.brands.get(request.brandId);
    if (!brand) {
      throw new Error('品牌不存在');
    }

    const now = TimezoneUtils.now();
    const profit = this.calculateProfit(request.budget, request.cost);

    const project: Project = {
      id,
      ...request,
      contractSigningStatus: request.contractSigningStatus || ContractSigningStatus.PENDING,
      profit,
      attachments: [],
      status: 'draft',
      createdAt: now,
      updatedAt: now,
      createdBy,
      updatedBy: createdBy
    };

    this.projects.set(id, project);

    // 记录创建变更日志（内存模式下暂不记录）

    // 发送项目创建通知给执行PM（内存模式下也发送）
    this.sendProjectCreatedNotification(project, createdBy).catch(error => {
      console.error('发送项目创建通知失败:', error);
    });

    return project;
  }

  // 获取项目列表
  async getProjects(params: ProjectQueryParams = {}): Promise<ProjectListResponse> {
    if (this.useDatabase) {
      const res = await db.getProjects(params);
      console.log('从数据库获取项目列表:', res);
      return res;
    }

    const {
      page = 1,
      pageSize = 20,
      documentType,
      brandId,
      contractType,
      contractSigningStatus,
      executorPM,
      status,
      keyword,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = params;

    let projects = Array.from(this.projects.values());

    // 应用过滤器
    if (documentType) {
      projects = projects.filter(p => p.documentType === documentType);
    }
    if (brandId) {
      projects = projects.filter(p => p.brandId === brandId);
    }
    if (contractType) {
      projects = projects.filter(p => p.contractType === contractType);
    }
    if (contractSigningStatus) {
      projects = projects.filter(p => p.contractSigningStatus === contractSigningStatus);
    }
    if (executorPM) {
      projects = projects.filter(p => p.executorPM === executorPM);
    }
    if (status) {
      projects = projects.filter(p => p.status === status);
    }
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase();
      projects = projects.filter(p =>
        p.projectName.toLowerCase().includes(lowerKeyword)
      );
    }
    if (startDate) {
      projects = projects.filter(p => p.period.startDate >= startDate);
    }
    if (endDate) {
      projects = projects.filter(p => p.period.endDate <= endDate);
    }

    // 排序
    projects.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'projectName':
          aValue = a.projectName;
          bValue = b.projectName;
          break;
        case 'profit':
          aValue = a.profit.profit;
          bValue = b.profit.profit;
          break;
        case 'updatedAt':
          aValue = a.updatedAt;
          bValue = b.updatedAt;
          break;
        default:
          aValue = a.createdAt;
          bValue = b.createdAt;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    // 分页
    const total = projects.length;
    const totalPages = Math.ceil(total / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedProjects = projects.slice(startIndex, endIndex);

    // 填充关联数据
    for (const project of paginatedProjects) {
      project.brand = this.brands.get(project.brandId);

      // 获取用户信息（模拟）
      try {
        project.executorPMInfo = await this.getUserInfo(project.executorPM);
        project.contentMediaInfo = await Promise.all(
          project.contentMediaIds.map(id => this.getUserInfo(id))
        );
      } catch (error) {
        console.warn('获取用户信息失败:', error);
      }
    }

    return {
      projects: paginatedProjects,
      total,
      page,
      pageSize,
      totalPages
    };
  }

  // 获取单个项目
  async getProject(id: string): Promise<Project | null> {
    if (this.useDatabase) {
      const project = await db.getProject(id);
      if (project) {
        // 填充用户信息
        try {
          project.executorPMInfo = await this.getUserInfo(project.executorPM);
          project.contentMediaInfo = await Promise.all(
            project.contentMediaIds.map(id => this.getUserInfo(id))
          );
        } catch (error) {
          console.warn('获取用户信息失败:', error);
        }
      }
      return project;
    }

    const project = this.projects.get(id);
    if (!project) {
      return null;
    }

    // 填充关联数据
    project.brand = this.brands.get(project.brandId);

    try {
      project.executorPMInfo = await this.getUserInfo(project.executorPM);
      project.contentMediaInfo = await Promise.all(
        project.contentMediaIds.map(id => this.getUserInfo(id))
      );
    } catch (error) {
      console.warn('获取用户信息失败:', error);
    }

    return project;
  }

  // 更新项目
  async updateProject(request: UpdateProjectRequest, updatedBy: string, operatorInfo?: { ip?: string; userAgent?: string }): Promise<Project> {
    if (this.useDatabase) {
      // 获取更新前的项目数据
      const beforeProject = await db.getProject(request.id);
      if (!beforeProject) {
        throw new Error('项目不存在');
      }

      const updatedProject = await db.updateProject(request, updatedBy);

      // 记录更新变更日志
      const userInfo = await this.getUserInfoForChangeLog(updatedBy);
      // 传递更新请求给字段对比方法，确保只对比用户实际提交的字段
      const changedFields = this.getChangedFields(beforeProject, updatedProject, request);

      if (changedFields.length > 0) {
        await this.createChangeLog({
          changeType: 'UPDATE',
          changeTitle: `更新项目: ${updatedProject.projectName}`,
          changeDetails: {
            action: 'update_project',
            updateData: request
          },
          beforeData: beforeProject,
          afterData: updatedProject,
          changedFields,
          operatorId: updatedBy,
          operatorName: userInfo.name,
          operatorIP: operatorInfo?.ip,
          userAgent: operatorInfo?.userAgent,
          description: `更新了项目"${updatedProject.projectName}"的${changedFields.join(', ')}字段`,
          projectId: request.id
        });
      }

      return updatedProject;
    }

    const project = this.projects.get(request.id);
    if (!project) {
      throw new Error('项目不存在');
    }

    // 验证品牌是否存在（如果更新了品牌）
    if (request.brandId && request.brandId !== project.brandId) {
      const brand = this.brands.get(request.brandId);
      if (!brand) {
        throw new Error('品牌不存在');
      }
    }

    // 更新项目数据
    const updatedProject: Project = {
      ...project,
      ...request,
      // 正确合并budget字段
      budget: request.budget ? { ...project.budget, ...request.budget } : project.budget,
      // 正确合并cost字段
      cost: request.cost ? { ...project.cost, ...request.cost } : project.cost,
      updatedAt: TimezoneUtils.now(),
      updatedBy
    };

    // 重新计算利润（如果预算或成本发生变化）
    if (request.budget || request.cost) {
      updatedProject.profit = this.calculateProfit(
        updatedProject.budget,
        updatedProject.cost
      );
    }

    this.projects.set(request.id, updatedProject);
    return updatedProject;
  }

  // 删除项目
  async deleteProject(id: string, deletedBy: string, operatorInfo?: { ip?: string; userAgent?: string }): Promise<boolean> {
    if (this.useDatabase) {
      // 获取删除前的项目数据
      const beforeProject = await db.getProject(id);
      if (!beforeProject) {
        throw new Error('项目不存在');
      }

      // 记录删除变更日志
      const userInfo = await this.getUserInfoForChangeLog(deletedBy);
      await this.createChangeLog({
        changeType: 'DELETE',
        changeTitle: `删除项目: ${beforeProject.projectName}`,
        changeDetails: {
          action: 'delete_project'
        },
        beforeData: beforeProject,
        changedFields: [], // 删除操作不涉及字段变更，使用空数组
        operatorId: deletedBy,
        operatorName: userInfo.name,
        operatorIP: operatorInfo?.ip,
        userAgent: operatorInfo?.userAgent,
        description: `删除了项目"${beforeProject.projectName}"`,
        projectId: id
      });

      return await db.deleteProject(id);
    }
    return this.projects.delete(id);
  }

  // 项目变更记录管理方法

  // 获取项目变更记录列表
  async getProjectChangeLogs(params: any = {}): Promise<any> {
    if (this.useDatabase) {
      return await db.getProjectChangeLogs(params);
    }

    // 内存模式下返回空列表
    return {
      changeLogs: [],
      total: 0,
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      totalPages: 0
    };
  }

  // 获取单个项目的变更记录
  async getProjectChangeLogsByProjectId(projectId: string, params?: {
    page?: number;
    pageSize?: number;
    sortOrder?: string;
  }): Promise<any> {
    if (this.useDatabase) {
      return await db.getProjectChangeLogsByProjectId(projectId, params);
    }

    // 内存模式下返回空列表
    return {
      changeLogs: [],
      total: 0,
      page: params?.page || 1,
      pageSize: params?.pageSize || 50,
      totalPages: 0
    };
  }

  // 品牌管理方法

  // 创建品牌
  async createBrand(request: CreateBrandRequest, createdBy: string): Promise<Brand> {
    if (this.useDatabase) {
      const dbBrand = await db.createBrand(request, createdBy);
      return this.transformDbBrand(dbBrand);
    }

    const id = this.generateId('brand');
    const now = TimezoneUtils.now();

    const brand: Brand = {
      id,
      ...request,
      status: 'active',
      createdAt: now,
      updatedAt: now,
      createdBy
    };

    this.brands.set(id, brand);
    return brand;
  }

  // 获取品牌列表
  async getBrands(params: BrandQueryParams = {}): Promise<BrandListResponse> {
    if (this.useDatabase) {
      const dbResult = await db.getBrands(params);
      return {
        ...dbResult,
        brands: dbResult.brands.map(brand => this.transformDbBrand(brand))
      };
    }

    const {
      page = 1,
      pageSize = 50,
      status,
      keyword,
      sortBy = 'name',
      sortOrder = 'asc'
    } = params;

    let brands = Array.from(this.brands.values());

    // 应用过滤器
    if (status) {
      brands = brands.filter(b => b.status === status);
    }
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase();
      brands = brands.filter(b =>
        b.name.toLowerCase().includes(lowerKeyword) ||
        (b.description && b.description.toLowerCase().includes(lowerKeyword))
      );
    }

    // 排序
    brands.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'updatedAt':
          aValue = a.updatedAt;
          bValue = b.updatedAt;
          break;
        default:
          aValue = a.createdAt;
          bValue = b.createdAt;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    // 分页
    const total = brands.length;
    const totalPages = Math.ceil(total / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedBrands = brands.slice(startIndex, endIndex);

    return {
      brands: paginatedBrands,
      total,
      page,
      pageSize,
      totalPages
    };
  }

  // 获取单个品牌
  async getBrand(id: string): Promise<Brand | null> {
    if (this.useDatabase) {
      const dbBrand = await db.getBrand(id);
      return dbBrand ? this.transformDbBrand(dbBrand) : null;
    }
    return this.brands.get(id) || null;
  }

  // 更新品牌
  async updateBrand(request: UpdateBrandRequest): Promise<Brand> {
    if (this.useDatabase) {
      const dbBrand = await db.updateBrand(request);
      return this.transformDbBrand(dbBrand);
    }

    const brand = this.brands.get(request.id);
    if (!brand) {
      throw new Error('品牌不存在');
    }

    const updatedBrand: Brand = {
      ...brand,
      ...request,
      updatedAt: TimezoneUtils.now()
    };

    this.brands.set(request.id, updatedBrand);
    return updatedBrand;
  }

  // 删除品牌
  async deleteBrand(id: string): Promise<boolean> {
    if (this.useDatabase) {
      return await db.deleteBrand(id);
    }

    // 检查是否有项目使用此品牌
    const projectsUsingBrand = Array.from(this.projects.values())
      .filter(p => p.brandId === id);

    if (projectsUsingBrand.length > 0) {
      throw new Error('无法删除品牌，存在关联的项目');
    }

    return this.brands.delete(id);
  }

  // 获取项目统计信息
  async getProjectStats(): Promise<ProjectStats> {
    if (this.useDatabase) {
      return await db.getProjectStats();
    }

    const projects = Array.from(this.projects.values());

    const totalProjects = projects.length;
    const activeProjects = projects.filter(p => p.status === 'active').length;
    const completedProjects = projects.filter(p => p.status === 'completed').length;

    const totalBudget = projects.reduce((sum, p) => sum + p.budget.planningBudget, 0);
    const totalProfit = projects.reduce((sum, p) => sum + p.profit.profit, 0);
    const averageGrossMargin = projects.length > 0
      ? projects.reduce((sum, p) => sum + p.profit.grossMargin, 0) / projects.length
      : 0;

    // 按品牌统计
    const brandStats = new Map<string, { count: number; totalBudget: number; brandName: string }>();
    projects.forEach(p => {
      const brand = this.brands.get(p.brandId);
      const brandName = brand?.name || '未知品牌';
      const existing = brandStats.get(p.brandId) || { count: 0, totalBudget: 0, brandName };
      existing.count++;
      existing.totalBudget += p.budget.planningBudget;
      brandStats.set(p.brandId, existing);
    });

    // 按合同类型统计
    const contractStats = new Map<string, { count: number; totalBudget: number }>();
    projects.forEach(p => {
      const existing = contractStats.get(p.contractType) || { count: 0, totalBudget: 0 };
      existing.count++;
      existing.totalBudget += p.budget.planningBudget;
      contractStats.set(p.contractType, existing);
    });

    // 获取其他统计数据
    const revenueStats = await this.getRevenueStats();
    const weeklyBudgetStats = await this.getWeeklyBudgetStats();
    const supplierStats = await this.getSupplierStats();

    return {
      totalProjects,
      activeProjects,
      completedProjects,
      totalBudget,
      totalProfit,
      averageGrossMargin: Math.round(averageGrossMargin * 100) / 100,
      revenueStats,
      weeklyBudgetStats,
      supplierStats,
      projectsByBrand: Array.from(brandStats.entries()).map(([brandId, stats]) => ({
        brandId,
        brandName: stats.brandName,
        count: stats.count,
        totalBudget: stats.totalBudget
      })),
      projectsByContractType: Array.from(contractStats.entries()).map(([contractType, stats]) => ({
        contractType: contractType as any,
        count: stats.count,
        totalBudget: stats.totalBudget
      }))
    };
  }

  // 获取用户信息（通过钉钉API）
  private async getUserInfo(userid: string): Promise<User> {
    try {
      // 尝试通过钉钉API获取用户信息
      // 暂时使用模拟数据，实际应该调用钉钉API
      return {
        userid,
        name: `用户${userid}`,
        avatar: '',
        department: '营销部'
      };
    } catch (error) {
      console.warn(`获取用户信息失败: ${userid}`, error);
      return {
        userid,
        name: `用户${userid}`,
        department: '未知部门'
      };
    }
  }

  // 项目文件上传处理 - 写入数据库
  async uploadProjectFile(file: any, uploadedBy: string, projectId?: string): Promise<FileUploadResponse & { id: string; uploadedAt: Date; uploadedBy: string }> {
    const fs = await import('fs');
    const path = await import('path');
    const { pipeline } = await import('stream/promises');

    try {
      // 生成唯一文件名
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substr(2, 9);
      const fileExtension = path.extname(file.filename);
      const filename = `${timestamp}_${randomStr}${fileExtension}`;

      // 确保上传目录存在
      const uploadDir = path.resolve(process.cwd(), 'uploads');
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      // 保存文件到磁盘
      const filePath = path.join(uploadDir, filename);
      const writeStream = fs.createWriteStream(filePath);
      await pipeline(file.file, writeStream);

      // 获取文件大小
      const stats = fs.statSync(filePath);
      const fileSize = stats.size;

      // 构建文件URL
      const fileUrl = `/uploads/${filename}`;

      if (this.useDatabase) {
        // 写入数据库
        const attachment = await db.createAttachment({
          filename,
          originalName: file.filename,
          size: fileSize,
          mimeType: file.mimetype || 'application/octet-stream',
          url: fileUrl,
          projectId: projectId || null, // 如果没有projectId，设为null
          uploadedBy
        });

        return {
          id: attachment.id,
          filename: attachment.filename,
          originalName: attachment.originalName,
          size: Number(attachment.size),
          mimeType: attachment.mimeType,
          url: attachment.url,
          uploadedAt: attachment.uploadedAt,
          uploadedBy: attachment.uploadedBy
        };
      } else {
        // 内存模式或临时上传（没有projectId）
        const id = this.generateId('file');
        const attachment: Attachment = {
          id,
          filename,
          originalName: file.filename,
          size: fileSize,
          mimeType: file.mimetype || 'application/octet-stream',
          url: fileUrl,
          uploadedAt: TimezoneUtils.now(),
          uploadedBy
        };

        this.attachments.set(id, attachment);

        return {
          id,
          filename,
          originalName: file.filename,
          size: fileSize,
          mimeType: file.mimetype || 'application/octet-stream',
          url: fileUrl,
          uploadedAt: attachment.uploadedAt,
          uploadedBy
        };
      }
    } catch (error) {
      console.error('文件上传失败:', error);
      throw new Error('文件上传失败');
    }
  }

  // 文件上传处理 (兼容旧接口)
  async uploadFile(file: any, uploadedBy: string): Promise<FileUploadResponse> {
    const result = await this.uploadProjectFile(file, uploadedBy);
    return {
      id: result.id,
      filename: result.filename,
      originalName: result.originalName,
      size: result.size,
      mimeType: result.mimeType,
      url: result.url
    };
  }

  // 获取附件信息
  async getAttachment(id: string): Promise<Attachment | null> {
    return this.attachments.get(id) || null;
  }

  // 项目收入管理方法

  // 创建项目收入
  async createProjectRevenue(projectId: string, request: CreateProjectRevenueRequest, createdBy: string): Promise<ProjectRevenue> {
    if (this.useDatabase) {
      return await db.createProjectRevenue(projectId, request, createdBy);
    }

    // 内存模式实现
    const id = this.generateId('revenue');
    const now = TimezoneUtils.now();

    const revenue: ProjectRevenue = {
      id,
      title: request.title,
      revenueType: request.revenueType,
      status: 'receiving' as RevenueStatus,
      plannedAmount: request.plannedAmount,
      plannedDate: request.plannedDate ? new Date(request.plannedDate) : TimezoneUtils.now(),
      milestone: request.milestone,
      paymentTerms: request.paymentTerms,
      notes: request.notes,
      projectId,
      createdAt: now,
      updatedAt: now,
      createdBy,
      updatedBy: createdBy
    };

    // 这里应该有一个收入存储，暂时返回创建的对象
    return revenue;
  }

  // 获取项目收入列表
  async getProjectRevenues(params: ProjectRevenueQueryParams = {}): Promise<ProjectRevenueListResponse> {
    if (this.useDatabase) {
      return await db.getProjectRevenues(params);
    }

    // 内存模式实现 - 返回空列表
    return {
      revenues: [],
      total: 0,
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      totalPages: 0
    };
  }

  // 获取单个项目收入
  async getProjectRevenue(id: string): Promise<ProjectRevenue | null> {
    if (this.useDatabase) {
      return await db.getProjectRevenue(id);
    }

    // 内存模式实现 - 返回null
    return null;
  }

  // 更新项目收入
  async updateProjectRevenue(request: UpdateProjectRevenueRequest, updatedBy: string): Promise<ProjectRevenue> {
    if (this.useDatabase) {
      return await db.updateProjectRevenue(request, updatedBy);
    }

    // 内存模式实现 - 抛出错误
    throw new Error('项目收入不存在');
  }

  // 删除项目收入
  async deleteProjectRevenue(id: string): Promise<boolean> {
    if (this.useDatabase) {
      return await db.deleteProjectRevenue(id);
    }

    // 内存模式实现 - 返回false
    return false;
  }

  // 获取收入统计
  async getRevenueStats(): Promise<RevenueStats> {
    if (this.useDatabase) {
      return await db.getRevenueStats();
    }

    // 内存模式实现 - 返回空统计
    return {
      totalPlannedRevenue: 0,
      totalActualRevenue: 0,
      totalInvoicedRevenue: 0,
      totalReceivedRevenue: 0,
      revenueByStatus: [],
      revenueByType: [],
      monthlyRevenueTrend: []
    };
  }

  // 确认项目收入
  async confirmProjectRevenue(id: string, request: any, updatedBy: string): Promise<ProjectRevenue> {
    if (this.useDatabase) {
      return await db.confirmProjectRevenue(id, request, updatedBy);
    }

    // 内存模式实现 - 抛出错误
    throw new Error('项目收入不存在');
  }

  // 批量确认项目收入
  async batchConfirmProjectRevenues(revenues: Array<{
    id: string;
    actualAmount: number;
    confirmedDate?: string;
    notes?: string;
  }>, updatedBy: string): Promise<any> {
    if (this.useDatabase) {
      return await db.batchConfirmProjectRevenues(revenues, updatedBy);
    }

    // 内存模式实现 - 返回空结果
    return {
      successCount: 0,
      failureCount: revenues.length,
      results: revenues.map(r => ({
        id: r.id,
        success: false,
        error: '内存模式不支持批量确认收入'
      }))
    };
  }

  // 供应商管理方法

  // 创建供应商
  async createSupplier(request: CreateSupplierRequest, createdBy: string): Promise<Supplier> {
    if (this.useDatabase) {
      return await db.createSupplier(request, createdBy);
    }

    // 内存模式实现
    const id = this.generateId('supplier');
    const now = TimezoneUtils.now();

    const supplier: Supplier = {
      id,
      name: request.name,
      shortName: request.shortName,
      code: request.code,
      contactPerson: request.contactPerson,
      contactPhone: request.contactPhone,
      contactEmail: request.contactEmail,
      address: request.address,
      taxNumber: request.taxNumber,
      bankAccount: request.bankAccount,
      bankName: request.bankName,
      legalPerson: request.legalPerson,
      serviceTypes: request.serviceTypes,
      preferredTaxRate: request.preferredTaxRate,
      creditLimit: request.creditLimit,
      paymentTerms: request.paymentTerms,
      status: 'active' as any,
      rating: request.rating,
      notes: request.notes,
      createdAt: now,
      updatedAt: now,
      createdBy,
      updatedBy: createdBy
    };

    return supplier;
  }

  // 获取供应商列表
  async getSuppliers(params: SupplierQueryParams = {}): Promise<SupplierListResponse> {
    return await db.getSuppliers(params);
  }

  // 获取单个供应商
  async getSupplier(id: string): Promise<Supplier | null> {
    if (this.useDatabase) {
      return await db.getSupplier(id);
    }

    // 内存模式实现 - 返回null
    return null;
  }

  // 更新供应商
  async updateSupplier(request: UpdateSupplierRequest, updatedBy: string): Promise<Supplier> {
    if (this.useDatabase) {
      return await db.updateSupplier(request, updatedBy);
    }

    // 内存模式实现 - 抛出错误
    throw new Error('供应商不存在');
  }

  // 删除供应商
  async deleteSupplier(id: string): Promise<boolean> {
    if (this.useDatabase) {
      return await db.deleteSupplier(id);
    }

    // 内存模式实现 - 返回false
    return false;
  }

  // 获取供应商统计
  async getSupplierStats(): Promise<SupplierStats> {
    if (this.useDatabase) {
      return await db.getSupplierStats();
    }

    // 内存模式实现 - 返回空统计
    return {
      totalSuppliers: 0,
      activeSuppliers: 0,
      suppliersByServiceType: [],
      suppliersByRating: []
    };
  }

  // 周预算管理方法

  // 创建周预算
  async createWeeklyBudget(projectId: string, request: CreateWeeklyBudgetRequest, createdBy: string, isImportingData?: boolean): Promise<WeeklyBudget> {
    const weeklyBudget = await db.createWeeklyBudget(projectId, request, createdBy);

    // 异步检查周预算是否超过项目成本10%
    // 开发环境不发送通知
    if (process.env.NODE_ENV !== 'development' && !isImportingData) {
      this.checkWeeklyBudgetExceeded(weeklyBudget, projectId).catch(error => {
        console.error('检查周预算超额失败:', error);
      });
    }

    return weeklyBudget;
  }

  // 获取周预算列表
  async getWeeklyBudgets(params: WeeklyBudgetQueryParams = {}): Promise<WeeklyBudgetListResponse> {
    if (this.useDatabase) {
      return await db.getWeeklyBudgets(params);
    }

    // 内存模式实现 - 返回空列表
    return {
      weeklyBudgets: [],
      total: 0,
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      totalPages: 0
    };
  }

  // 获取单个周预算
  async getWeeklyBudget(id: string): Promise<WeeklyBudget | null> {
    if (this.useDatabase) {
      return await db.getWeeklyBudget(id);
    }

    // 内存模式实现 - 返回null
    return null;
  }

  // 更新周预算
  async updateWeeklyBudget(request: UpdateWeeklyBudgetRequest, updatedBy: string): Promise<WeeklyBudget> {
    if (this.useDatabase) {
      const weeklyBudget = await db.updateWeeklyBudget(request, updatedBy);

      // 如果更新了合同金额，检查是否超过项目成本10%
      if (request.contractAmount !== undefined) {
        this.checkWeeklyBudgetExceeded(weeklyBudget, weeklyBudget.projectId).catch(error => {
          console.error('检查周预算超额失败:', error);
        });
      }

      return weeklyBudget;
    }

    // 内存模式实现 - 抛出错误
    throw new Error('周预算不存在');
  }

  // 删除周预算
  async deleteWeeklyBudget(id: string): Promise<boolean> {
    if (this.useDatabase) {
      return await db.deleteWeeklyBudget(id);
    }

    // 内存模式实现 - 返回false
    return false;
  }

  // 获取周预算统计
  async getWeeklyBudgetStats(): Promise<WeeklyBudgetStats> {
    if (this.useDatabase) {
      return await db.getWeeklyBudgetStats();
    }

    // 内存模式实现 - 返回空统计
    return {
      totalBudgets: 0,
      totalContractAmount: 0,
      totalPaidAmount: 0,
      totalRemainingAmount: 0,
      budgetsByServiceType: [],
      budgetsByStatus: [],
      budgetsBySupplier: [],
      weeklyTrend: []
    };
  }

  // 批量创建周预算
  async batchCreateWeeklyBudgets(
    projectId: string,
    startDate: string,
    endDate: string,
    serviceType: ServiceType,
    defaultContractAmount: number,
    defaultTaxRate: TaxRate,
    createdBy: string
  ): Promise<WeeklyBudget[]> {
    if (this.useDatabase) {
      const weeklyBudgets = await db.batchCreateWeeklyBudgets(projectId, startDate, endDate, serviceType, defaultContractAmount, defaultTaxRate, createdBy);

      // 异步检查每个周预算是否超过项目成本10%
      weeklyBudgets.forEach(weeklyBudget => {
        this.checkWeeklyBudgetExceeded(weeklyBudget, projectId).catch(error => {
          console.error('检查周预算超额失败:', error);
        });
      });

      return weeklyBudgets;
    }

    // 内存模式实现 - 返回空数组
    return [];
  }

  // 辅助方法：获取周数
  private getWeekNumber(date: Date): number {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }

  // 财务报表方法

  // 获取品牌财务汇总报表
  async getBrandFinancialSummary(params: FinancialReportQueryParams = {}): Promise<FinancialReportResponse> {
    if (this.useDatabase) {
      return await db.getBrandFinancialSummary(params);
    }

    // 内存模式实现
    const brands = Array.from(this.brands.values());
    const projects = Array.from(this.projects.values());

    // 应用过滤器
    let filteredProjects = projects;
    if (params.brandId) {
      filteredProjects = filteredProjects.filter(p => p.brandId === params.brandId);
    }
    if (params.startDate) {
      const startDate = typeof params.startDate === 'string' ? TimezoneUtils.parseDate(params.startDate) : params.startDate;
      filteredProjects = filteredProjects.filter(p => p.period.startDate >= startDate);
    }
    if (params.endDate) {
      const endDate = typeof params.endDate === 'string' ? TimezoneUtils.parseDate(params.endDate) : params.endDate;
      filteredProjects = filteredProjects.filter(p => p.period.endDate <= endDate);
    }
    if (params.projectStatus) {
      filteredProjects = filteredProjects.filter(p => params.projectStatus!.includes(p.status as ProjectStatus));
    }

    // 按品牌分组计算财务数据
    const brandSummaries: BrandFinancialSummary[] = [];
    let totalOrderAmount = 0;
    let totalExecutedAmount = 0;
    let totalEstimatedProfit = 0;
    let totalReceivedAmount = 0;
    let totalPaidAmount = 0;

    for (const brand of brands) {
      const brandProjects = filteredProjects.filter(p => p.brandId === brand.id);

      if (brandProjects.length === 0 && params.brandId !== brand.id) {
        continue; // 跳过没有项目的品牌（除非特定查询某个品牌）
      }

      const summary = await this.calculateBrandFinancialSummary(brand, brandProjects);
      brandSummaries.push(summary);

      totalOrderAmount += summary.orderAmount;
      totalExecutedAmount += summary.executedAmount;
      totalEstimatedProfit += summary.estimatedProfit;
      totalReceivedAmount += summary.receivedAmount;
      totalPaidAmount += summary.paidProjectAmount;
    }

    const overallProfitMargin = totalOrderAmount > 0 ? (totalEstimatedProfit / totalOrderAmount) * 100 : 0;

    return {
      summary: {
        totalBrands: brandSummaries.length,
        totalOrderAmount,
        totalExecutedAmount,
        totalEstimatedProfit,
        totalReceivedAmount,
        totalPaidAmount,
        overallProfitMargin: Math.round(overallProfitMargin * 100) / 100
      },
      brands: brandSummaries,
      generatedAt: TimezoneUtils.now(),
      reportPeriod: {
        startDate: params.startDate ? (typeof params.startDate === 'string' ? TimezoneUtils.parseDate(params.startDate) : params.startDate) : undefined,
        endDate: params.endDate ? (typeof params.endDate === 'string' ? TimezoneUtils.parseDate(params.endDate) : params.endDate) : undefined
      }
    };
  }

  // 获取品牌财务详细报表
  async getBrandFinancialDetail(brandId: string, params: FinancialReportQueryParams = {}): Promise<BrandFinancialDetail> {
    if (this.useDatabase) {
      return await db.getBrandFinancialDetail(brandId, params);
    }

    // 内存模式实现
    const brand = this.brands.get(brandId);
    if (!brand) {
      throw new Error('品牌不存在');
    }

    let brandProjects = Array.from(this.projects.values()).filter(p => p.brandId === brandId);

    // 应用过滤器
    if (params.startDate) {
      const startDate = typeof params.startDate === 'string' ? TimezoneUtils.parseDate(params.startDate) : params.startDate;
      brandProjects = brandProjects.filter(p => p.period.startDate >= startDate);
    }
    if (params.endDate) {
      const endDate = typeof params.endDate === 'string' ? TimezoneUtils.parseDate(params.endDate) : params.endDate;
      brandProjects = brandProjects.filter(p => p.period.endDate <= endDate);
    }
    if (params.projectStatus) {
      brandProjects = brandProjects.filter(p => params.projectStatus!.includes(p.status as ProjectStatus));
    }

    const summary = await this.calculateBrandFinancialSummary(brand, brandProjects);

    // 构建详细项目信息
    const projectDetails = await Promise.all(brandProjects.map(async (project) => {
      const totalBudget = project.budget.planningBudget + project.budget.influencerBudget +
        project.budget.adBudget + project.budget.otherBudget;
      const totalCost = project.cost.influencerCost + project.cost.adCost + project.cost.otherCost;

      // 获取用户信息
      let executorPMInfo;
      try {
        const userInfo = await this.getUserInfo(project.executorPM);
        if (userInfo) {
          executorPMInfo = {
            userid: userInfo.userid,
            name: userInfo.name,
            department: userInfo.department || ''
          };
        }
      } catch (error) {
        console.warn('获取用户信息失败:', error);
      }

      return {
        id: project.id,
        projectName: project.projectName,
        status: project.status as ProjectStatus,
        documentType: project.documentType,
        contractType: project.contractType,
        period: project.period,
        budget: {
          planningBudget: project.budget.planningBudget,
          totalBudget
        },
        cost: {
          totalCost,
          estimatedInfluencerRebate: project.cost.estimatedInfluencerRebate
        },
        profit: project.profit,
        revenue: {
          plannedAmount: 0, // 需要从收入表获取
          receivedAmount: 0, // 需要从收入表获取
          unreceivedAmount: 0
        },
        weeklyBudgets: {
          totalContractAmount: 0, // 需要从周预算表获取
          paidAmount: 0, // 需要从周预算表获取
          unpaidAmount: 0
        },
        executorPM: project.executorPM,
        executorPMInfo
      };
    }));

    return {
      brandInfo: {
        id: brand.id,
        name: brand.name,
        description: brand.description,
        logo: brand.logo
      },
      summary,
      projects: projectDetails,
      revenueAnalysis: {
        totalPlannedRevenue: 0,
        totalReceivedRevenue: 0,
        revenueByStatus: [],
        monthlyTrend: []
      },
      costAnalysis: {
        totalWeeklyBudgets: 0,
        totalPaidAmount: 0,
        totalUnpaidAmount: 0,
        budgetsByServiceType: []
      }
    };
  }

  // 计算品牌财务汇总
  private async calculateBrandFinancialSummary(brand: Brand, projects: Project[]): Promise<BrandFinancialSummary> {
    let orderAmount = 0;
    let executedAmount = 0;
    let executingAmount = 0;
    let estimatedProfit = 0;
    let receivedAmount = 0;
    let unreceivedAmount = 0;
    let paidProjectAmount = 0;
    let unpaidProjectAmount = 0;

    let activeProjectCount = 0;
    let completedProjectCount = 0;

    for (const project of projects) {
      // 品牌下单金额 = 项目规划预算总和
      orderAmount += project.budget.planningBudget;

      // 预估毛利 = 项目利润总和
      estimatedProfit += project.profit.profit;

      // 根据项目状态计算已执行和执行中金额
      if (project.status === 'completed') {
        completedProjectCount++;
        // 已执行金额 = 已完成项目的实际支出（成本，包含居间费）
        const totalCost = project.cost.influencerCost + project.cost.adCost + project.cost.otherCost + project.cost.intermediaryCost;
        executedAmount += totalCost;
      } else if (project.status === 'active') {
        activeProjectCount++;
        // 执行中项目金额 = 进行中项目的预算
        executingAmount += project.budget.planningBudget;
      } else if (project.status === 'draft') {
        // 草稿状态的项目不计入执行金额
      } else if (project.status === 'cancelled') {
        // 已取消的项目不计入执行金额
      }

      // 已回款和未回款
      if (project.revenues && project.revenues.length > 0) {
        for (const revenue of project.revenues) {
          if (revenue.status === 'received') {
            receivedAmount += revenue.actualAmount || revenue.plannedAmount;
          } else {
            unreceivedAmount += revenue.plannedAmount;
          }
        }
      }

      // 已支付和未支付
      if (project.weeklyBudgets && project.weeklyBudgets.length > 0) {
        for (const budget of project.weeklyBudgets) {
          paidProjectAmount += budget.paidAmount;
          unpaidProjectAmount += budget.contractAmount - budget.paidAmount;
        }
      }

    }

    // 计算预估毛利率
    const estimatedProfitMargin = orderAmount > 0 ? (estimatedProfit / orderAmount) * 100 : 0;

    return {
      brandId: brand.id,
      brandName: brand.name,
      orderAmount: Math.round(orderAmount * 100) / 100,
      executedAmount: Math.round(executedAmount * 100) / 100,
      executingAmount: Math.round(executingAmount * 100) / 100,
      estimatedProfit: Math.round(estimatedProfit * 100) / 100,
      estimatedProfitMargin: Math.round(estimatedProfitMargin * 100) / 100,
      receivedAmount: Math.round(receivedAmount * 100) / 100,
      unreceivedAmount: Math.round(unreceivedAmount * 100) / 100,
      paidProjectAmount: Math.round(paidProjectAmount * 100) / 100,
      unpaidProjectAmount: Math.round(unpaidProjectAmount * 100) / 100,
      projectCount: projects.length,
      activeProjectCount,
      completedProjectCount
    };
  }

  // 通知相关方法

  /**
   * 发送项目创建通知给执行PM
   */
  private async sendProjectCreatedNotification(project: Project, createdBy: string): Promise<void> {
    try {
      // 获取执行PM用户信息
      const executorPMInfo = await this.userSyncService.getUserInfo(project.executorPM);
      if (!executorPMInfo) {
        console.warn(`无法获取执行PM用户信息: ${project.executorPM}`);
        return;
      }

      // 获取创建人用户信息
      const creatorInfo = await this.userSyncService.getUserInfo(createdBy);
      const creatorName = creatorInfo?.name || createdBy;

      // 获取品牌信息
      const brand = await this.getBrand(project.brandId);
      const brandName = brand?.name || '未知品牌';

      // 构建通知数据
      const defaultDate = TimezoneUtils.formatToLocalDate(TimezoneUtils.now());
      const startDate = project.period?.startDate
        ? (project.period.startDate instanceof Date
          ? TimezoneUtils.formatToLocalDate(project.period.startDate)
          : TimezoneUtils.formatToLocalDate(new Date(project.period.startDate)))
        : defaultDate;

      const endDate = project.period?.endDate
        ? (project.period.endDate instanceof Date
          ? TimezoneUtils.formatToLocalDate(project.period.endDate)
          : TimezoneUtils.formatToLocalDate(new Date(project.period.endDate)))
        : defaultDate;

      const notificationData = {
        projectName: project.projectName,
        brandName,
        executorPMName: executorPMInfo.name,
        budget: project.budget.planningBudget,
        startDate: startDate as string,
        endDate: endDate as string,
        creatorName
      };

      // 发送通知
      const success = await this.notificationService.sendProjectCreatedNotification(
        project.executorPM,
        notificationData
      );

      if (success) {
        console.log(`项目创建通知发送成功: ${project.projectName} -> ${executorPMInfo.name}`);
      } else {
        console.error(`项目创建通知发送失败: ${project.projectName} -> ${executorPMInfo.name}`);
      }
    } catch (error) {
      console.error('发送项目创建通知时出错:', error);
    }
  }

  /**
   * 检查周预算是否超过项目成本10%并发送通知
   */
  async checkWeeklyBudgetExceeded(weeklyBudget: WeeklyBudget, projectId: string): Promise<void> {
    try {
      // 获取项目信息
      const project = await this.getProject(projectId);
      if (!project) {
        console.warn(`无法获取项目信息: ${projectId}`);
        return;
      }

      // 计算项目总成本（包含居间费）
      const totalProjectCost = project.cost.influencerCost + project.cost.adCost + project.cost.otherCost + project.cost.intermediaryCost;

      // 计算超额比例
      const exceedPercentage = (weeklyBudget.contractAmount / totalProjectCost) * 100;

      // 如果超过10%，发送通知
      if (exceedPercentage > 10) {
        // 获取项目创建人用户信息
        const creatorInfo = await this.userSyncService.getUserInfo(project.createdBy);
        if (!creatorInfo) {
          console.warn(`无法获取项目创建人用户信息: ${project.createdBy}`);
          return;
        }

        // 获取品牌信息
        const brand = await this.getBrand(project.brandId);
        const brandName = brand?.name || '未知品牌';

        // 构建通知数据
        const notificationData = {
          projectName: project.projectName,
          brandName,
          weeklyBudgetTitle: weeklyBudget.title,
          contractAmount: weeklyBudget.contractAmount,
          projectCost: totalProjectCost,
          exceedPercentage,
          creatorName: creatorInfo.name
        };

        // 发送通知
        const success = await this.notificationService.sendWeeklyBudgetExceededNotification(
          project.createdBy,
          notificationData
        );

        if (success) {
          console.log(`周预算超额通知发送成功: ${weeklyBudget.title} -> ${creatorInfo.name}`);
        } else {
          console.error(`周预算超额通知发送失败: ${weeklyBudget.title} -> ${creatorInfo.name}`);
        }
      }
    } catch (error) {
      console.error('检查周预算超额时出错:', error);
    }
  }

  /**
   * 批量上传周预算（CSV文件处理）
   */
  async bulkUploadWeeklyBudgets(csvContent: any[], createdBy: string) {
    try {
      // 解析CSV数据
      const parsedData = this.parseWeeklyBudgetCSV(csvContent);

      const results = {
        totalRows: parsedData.length,
        successCount: 0,
        failedCount: 0,
        createdSuppliers: [] as string[],
        createdBudgets: [] as string[],
        createdApprovals: [] as string[],
        errors: [] as Array<{ row: number; error: string; data: any }>
      };

      for (let i = 0; i < parsedData.length; i++) {
        const rowData = parsedData[i];
        if (!rowData) continue;

        try {
          // 1. 通过项目名称查找项目
          const project = await this.findProjectByName(rowData.projectName);
          if (!project) {
            results.errors.push({
              row: i + 2, // CSV行号（包含标题行）
              error: `项目 "${rowData.projectName}" 不存在`,
              data: rowData
            });
            results.failedCount++;
            continue;
          }

          // 2. 检查或创建供应商
          let supplier = await this.findSupplierByNameExact(rowData.supplierName);
          if (!supplier) {
            supplier = await this.createSupplierFromCSVData(rowData, createdBy);
            results.createdSuppliers.push(supplier.name);
          }

          // 3. 创建周预算
          const weeklyBudget = await this.createWeeklyBudgetFromCSVData(
            project,
            supplier.id,
            rowData,
            createdBy
          );
          results.createdBudgets.push(weeklyBudget.id);

          // 4. 如果有已付金额，创建审批记录
          if (rowData.paidAmount > 0) {
            const approval = await this.createCompletedApprovalFromCSVData(
              weeklyBudget.id,
              rowData,
              createdBy
            );
            results.createdApprovals.push(approval.id);
          }

          results.successCount++;
        } catch (error) {
          console.error(`处理第 ${i + 2} 行数据失败:`, error);
          results.errors.push({
            row: i + 2,
            error: error instanceof Error ? error.message : '未知错误',
            data: rowData
          });
          results.failedCount++;
        }
      }

      return results;
    } catch (error) {
      console.error('批量上传周预算失败:', error);
      throw new Error(`批量上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 解析周预算CSV数据
   */
  private parseWeeklyBudgetCSV(csvContent: any[]): WeeklyBudgetCSVData[] {
    const lines = csvContent;
    if (lines.length < 2) {
      throw new Error('CSV文件格式错误：至少需要标题行和一行数据');
    }

    // 跳过标题行
    const rows = lines.slice(1);
    const results = [];
    if (!rows) {
      return [];
    }
    if (rows.length === 0) {
      throw new Error('CSV文件中没有数据行');
    }

    // 过滤空行(全是空字符串)
    const dataLines = rows.filter(line => line.some((cell: string) => cell.trim() !== ''));

    for (let i = 0; i < dataLines.length; i++) {
      if (!dataLines[i]) continue;
      if (dataLines[i] === '') continue;
      if (dataLines[i] === undefined) continue;

      const line = dataLines[i];
      if (!line) continue; // 跳过空行

      const columns = line

      if (columns.length < 9) {
        throw new Error(`第 ${i + 2} 行数据格式错误：列数不足`);
      }

      try {
        const rowData: WeeklyBudgetCSVData = {
          projectName: columns[0] || '',
          supplierName: columns[1] || '',
          serviceType: columns[2] || '',
          serviceContent: columns[3] || '',
          contractAmount: this.parseAmount(columns[4] || '0'), // 供应商采购成本
          taxRate: this.parseTaxRate(columns[5] || '0'), // 专票税率
          taxExclusiveAmount: this.parseAmount(columns[6] || '0'), // 不含税金额
          paidAmount: this.parseAmount(columns[7] || '0'), // 已付金额
          unpaidAmount: this.parseAmount(columns[8] || '0') // 未付款金额
        };

        // 验证必填字段
        if (!rowData.projectName || !rowData.supplierName) {
          throw new Error('项目名称和供应商名称不能为空');
        }

        results.push(rowData);
      } catch (error) {
        throw new Error(`第 ${i + 2} 行数据解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    return results;
  }

  /**
   * 解析金额字符串（支持万为单位）
   */
  private parseAmount(amountStr: string): number {
    if (!amountStr || amountStr === '0' || amountStr === '0.0') {
      return 0;
    }

    // 移除空格和特殊字符
    const cleanStr = amountStr.replace(/[,\s]/g, '');

    // 检查是否以"万"结尾
    if (cleanStr.endsWith('万')) {
      const numStr = cleanStr.slice(0, -1);
      const num = parseFloat(numStr);
      if (isNaN(num)) {
        throw new Error(`无效的金额格式: ${amountStr}`);
      }
      return num * 10000; // 转换为元
    }

    const num = parseFloat(cleanStr);
    if (isNaN(num)) {
      throw new Error(`无效的金额格式: ${amountStr}`);
    }
    return num;
  }

  /**
   * 解析税率
   */
  private parseTaxRate(taxRateStr: string): TaxRate {
    const cleanStr = taxRateStr.replace(/[%\s]/g, '');

    switch (cleanStr) {
      case '1':
        return TaxRate.SPECIAL_1;
      case '3':
        return TaxRate.SPECIAL_3;
      case '6':
        return TaxRate.SPECIAL_6;
      case '0':
      case '无税':
      case '无税票':
      case '':
        return TaxRate.NONE;
      default:
        return TaxRate.GENERAL;
    }
  }

  /**
   * 通过项目名称查找项目
   */
  private async findProjectByName(projectName: string): Promise<Project | null> {
    if (this.useDatabase) {
      // 使用数据库查询
      return await db.getProjectByName(projectName);
    }

    // 内存模式实现
    for (const project of this.projects.values()) {
      if (project.projectName === projectName) {
        return project;
      }
    }
    return null;
  }

  /**
   * 通过供应商名称查找供应商
   */
  private async findSupplierByName(supplierName: string): Promise<Supplier | null> {
    if (this.useDatabase) {
      const suppliers = await db.getSuppliers({ keyword: supplierName, pageSize: 1 });
      return suppliers.suppliers.find(s => s.name === supplierName) || null;
    }

    // 内存模式实现 - 返回null
    return null;
  }

  /**
   * 通过供应商名称查找供应商（全匹配）
   */
  private async findSupplierByNameExact(supplierName: string): Promise<Supplier | null> {
    if (this.useDatabase) {
      return await db.getSupplierByName(supplierName);
    }

    // 内存模式实现 - 返回null
    return null;
  }

  /**
   * 从CSV数据创建供应商
   */
  private async createSupplierFromCSVData(csvData: WeeklyBudgetCSVData, createdBy: string): Promise<Supplier> {
    const serviceType = this.mapServiceTypeFromCSV(csvData.serviceType);

    const supplierData: CreateSupplierRequest = {
      name: csvData.supplierName,
      shortName: csvData.supplierName,
      serviceTypes: [serviceType],
      preferredTaxRate: csvData.taxRate,
      contactPerson: '待补充',
      contactPhone: '待补充',
      address: '待补充',
      notes: `从CSV批量导入创建，项目：${csvData.projectName}`
    };

    if (this.useDatabase) {
      return await db.createSupplier(supplierData, createdBy);
    }

    // 内存模式实现 - 抛出错误
    throw new Error('内存模式不支持创建供应商');
  }

  /**
   * 映射CSV中的服务类型到系统枚举
   */
  private mapServiceTypeFromCSV(serviceTypeStr: string): ServiceType {
    const lowerStr = serviceTypeStr.toLowerCase();
    if (lowerStr.includes('达人') || lowerStr.includes('influencer')) {
      return ServiceType.INFLUENCER;
    } else if (lowerStr.includes('广告') || lowerStr.includes('投流') || lowerStr.includes('advertising')) {
      return ServiceType.ADVERTISING;
    } else {
      return ServiceType.OTHER;
    }
  }

  /**
   * 从CSV数据创建预算
   */
  private async createBudgetFromCSVData(
    project: Project,
    supplierId: string,
    csvData: WeeklyBudgetCSVData,
    createdBy: string
  ): Promise<Budget> {
    const budgetData: CreateBudgetRequest = {
      title: `${csvData.supplierName}-${csvData.serviceContent}`,
      serviceType: this.mapServiceTypeFromCSV(csvData.serviceType),
      serviceContent: csvData.serviceContent,
      contractAmount: csvData.contractAmount,
      taxRate: csvData.taxRate,
      supplierId: supplierId,
      remarks: `从CSV批量导入，项目：${csvData.projectName}`
    };

    return await this.createBudget(project.id, budgetData, createdBy);
  }

  /**
   * 从CSV数据创建资金计划
   */
  private async createFundingPlanFromCSVData(
    budget: Budget,
    csvData: WeeklyBudgetCSVData,
    createdBy: string
  ): Promise<FundingPlan> {
    const now = TimezoneUtils.now();

    // 计算年月周信息
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const weekOfMonth = this.getWeekOfMonth(now);

    const fundingPlanData: CreateFundingPlanRequest = {
      title: `${csvData.supplierName}-${month}月第${weekOfMonth}周-${csvData.serviceContent}`,
      year: year,
      month: month,
      weekOfMonth: weekOfMonth,
      plannedAmount: csvData.paidAmount, // 计划金额等于已付金额
      budgetId: budget.id,
      remarks: `从CSV批量导入，已付金额：${csvData.paidAmount}元`
    };

    return await this.createFundingPlan(fundingPlanData, createdBy);
  }

  /**
   * 计算当月第几周
   */
  private getWeekOfMonth(date: Date): number {
    const firstDayOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
    const dayOfMonth = date.getDate();
    const firstWeekday = firstDayOfMonth.getDay();

    // 计算当月第几周（从1开始）
    return Math.ceil((dayOfMonth + firstWeekday) / 7);
  }

  /**
   * 从CSV数据创建已完成的审批记录（针对资金计划）
   */
  private async createCompletedApprovalFromCSVData(
    fundingPlanId: string,
    csvData: WeeklyBudgetCSVData,
    createdBy: string
  ): Promise<any> {
    // 创建已完成的审批记录
    const approvalData = {
      processInstanceId: `csv_import_${Date.now()}`,
      processCode: 'FUNDING_PLAN_PAYMENT',
      businessId: fundingPlanId,
      fundingPlanId: fundingPlanId,
      title: `CSV导入-${csvData.supplierName}-${csvData.serviceContent}`,
      originatorUserId: createdBy,
      status: 'APPROVED' as any,
      createTime: new Date(),
      approvalAmount: csvData.paidAmount,
      reason: `CSV批量导入的已付款项，金额：${csvData.paidAmount}元`
    };

    // 使用数据库服务创建审批实例
    if (this.useDatabase) {
      const approval = await db.createApprovalInstance(approvalData);

      // 更新资金计划的已付金额
      await this.updateFundingPlanPaidAmount(fundingPlanId, csvData.paidAmount);

      return approval;
    }

    // Mock实现
    return {
      id: `approval_${Date.now()}`,
      fundingPlanId,
      status: 'APPROVED',
      approvalAmount: csvData.paidAmount,
      reason: approvalData.reason,
      createdBy,
      createdAt: new Date()
    };
  }

  /**
   * 更新资金计划的已付金额
   */
  private async updateFundingPlanPaidAmount(fundingPlanId: string, paidAmount: number): Promise<void> {
    if (this.useDatabase) {
      // 获取当前资金计划
      const fundingPlan = await db.getFundingPlan(fundingPlanId);
      if (!fundingPlan) {
        throw new Error('资金计划不存在');
      }

      // 更新已付金额和剩余金额
      const updateData = {
        id: fundingPlanId,
        paidAmount: paidAmount,
        remainingAmount: fundingPlan.plannedAmount - paidAmount
      };

      await db.updateFundingPlan(updateData, 'system');
    }
  }

  // 预算管理 - 重构后的预算管理方法
  async createBudget(projectId: string, data: CreateBudgetRequest, createdBy: string): Promise<Budget> {
    if (this.useDatabase) {
      return await db.createBudget(projectId, data, createdBy);
    }

    // Mock实现
    const budget: Budget = {
      id: `budget_${Date.now()}`,
      title: data.title,
      serviceType: data.serviceType,
      serviceContent: data.serviceContent,
      remarks: data.remarks,
      contractAmount: data.contractAmount,
      taxRate: data.taxRate,
      totalPaidAmount: 0,
      remainingAmount: data.contractAmount,
      status: 'CREATED' as any,
      projectId,
      supplierId: data.supplierId,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy,
      updatedBy: createdBy,
    };

    return budget;
  }

  async getBudgets(params: BudgetQueryParams = {}): Promise<BudgetListResponse> {
    if (this.useDatabase) {
      return await db.getBudgets(params);
    }

    // Mock实现
    return {
      budgets: [],
      total: 0,
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      totalPages: 0
    };
  }

  async getBudget(id: string): Promise<Budget | null> {
    if (this.useDatabase) {
      return await db.getBudget(id);
    }

    // Mock实现
    return null;
  }

  async updateBudget(data: UpdateBudgetRequest, updatedBy: string): Promise<Budget> {
    if (this.useDatabase) {
      return await db.updateBudget(data, updatedBy);
    }

    // Mock实现
    throw new Error('Budget not found');
  }

  async deleteBudget(id: string): Promise<boolean> {
    if (this.useDatabase) {
      return await db.deleteBudget(id);
    }

    // Mock实现
    return true;
  }

  async getBudgetStats(): Promise<BudgetStats> {
    if (this.useDatabase) {
      return await db.getBudgetStats();
    }

    // Mock实现
    return {
      totalBudgets: 0,
      totalContractAmount: 0,
      totalPaidAmount: 0,
      totalRemainingAmount: 0,
      budgetsByServiceType: [],
      budgetsByStatus: [],
      budgetsBySupplier: []
    };
  }

  // 资金计划管理
  async createFundingPlan(data: CreateFundingPlanRequest, createdBy: string): Promise<FundingPlan> {
    if (this.useDatabase) {
      return await db.createFundingPlan(data, createdBy);
    }

    // Mock实现
    const fundingPlan: FundingPlan = {
      id: `funding_plan_${Date.now()}`,
      title: data.title,
      year: data.year,
      month: data.month,
      weekOfMonth: data.weekOfMonth,
      plannedAmount: data.plannedAmount,
      paidAmount: 0,
      remainingAmount: data.plannedAmount,
      status: 'DRAFT' as any,
      approvalStatus: 'NONE' as any,
      remarks: data.remarks,
      budgetId: data.budgetId,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy,
      updatedBy: createdBy,
    };

    return fundingPlan;
  }

  async getFundingPlans(params: FundingPlanQueryParams = {}): Promise<FundingPlanListResponse> {
    if (this.useDatabase) {
      return await db.getFundingPlans(params);
    }

    // Mock实现
    return {
      fundingPlans: [],
      total: 0,
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      totalPages: 0
    };
  }

  async getFundingPlan(id: string): Promise<FundingPlan | null> {
    if (this.useDatabase) {
      return await db.getFundingPlan(id);
    }

    // Mock实现
    return null;
  }

  async updateFundingPlan(data: UpdateFundingPlanRequest, updatedBy: string): Promise<FundingPlan> {
    if (this.useDatabase) {
      return await db.updateFundingPlan(data, updatedBy);
    }

    // Mock实现
    throw new Error('FundingPlan not found');
  }

  async deleteFundingPlan(id: string): Promise<boolean> {
    if (this.useDatabase) {
      return await db.deleteFundingPlan(id);
    }

    // Mock实现
    return true;
  }

  // 批量创建资金计划 - 严格事务模式
  async createBatchFundingPlans(plans: CreateFundingPlanRequest[], createdBy: string): Promise<FundingPlan[]> {
    if (this.useDatabase) {
      return await db.createBatchFundingPlans(plans, createdBy);
    }

    // Mock实现 - 也采用严格事务模式
    const createdPlans: FundingPlan[] = [];

    // 预先验证所有计划
    for (let i = 0; i < plans.length; i++) {
      const plan = plans[i];
      if (!plan) {
        throw new Error(`计划数据为空 (第${i + 1}条)`);
      }

      // 这里可以添加更多的预验证逻辑
      if (!plan.budgetId) {
        throw new Error(`预算ID不能为空 (第${i + 1}条)`);
      }

      if (plan.plannedAmount <= 0) {
        throw new Error(`计划金额必须大于0 (第${i + 1}条)`);
      }
    }

    // 如果所有验证通过，则创建所有计划
    for (let i = 0; i < plans.length; i++) {
      const plan = plans[i];
      if (!plan) continue; // 这个检查在上面已经做过了，但为了类型安全还是保留

      const createdPlan = await this.createFundingPlan(plan, createdBy);
      createdPlans.push(createdPlan);
    }

    return createdPlans;
  }

  async getFundingPlanStats(): Promise<FundingPlanStats> {
    if (this.useDatabase) {
      return await db.getFundingPlanStats();
    }

    // Mock实现
    return {
      totalPlans: 0,
      totalPlannedAmount: 0,
      totalPaidAmount: 0,
      totalRemainingAmount: 0,
      plansByStatus: [],
      plansByApprovalStatus: [],
      monthlyTrend: []
    };
  }
}
